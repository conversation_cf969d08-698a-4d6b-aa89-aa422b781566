package benchmark

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"testing"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// BenchmarkConfig holds configuration for benchmark tests
type BenchmarkConfig struct {
	ServiceName     string
	TestName        string
	Concurrency     int
	Duration        time.Duration
	WarmupDuration  time.Duration
	ReportInterval  time.Duration
	MetricsEnabled  bool
}

// BenchmarkResult holds the results of a benchmark test
type BenchmarkResult struct {
	ServiceName    string
	TestName       string
	TotalOps       int64
	Duration       time.Duration
	OpsPerSecond   float64
	AvgLatency     time.Duration
	P95Latency     time.Duration
	P99Latency     time.Duration
	ErrorRate      float64
	MemoryUsage    int64
	CPUUsage       float64
	Timestamp      time.Time
}

// BenchmarkFramework provides utilities for running performance benchmarks
type BenchmarkFramework struct {
	config  *BenchmarkConfig
	metrics *BenchmarkMetrics
	results []BenchmarkResult
	mu      sync.RWMutex
}

// BenchmarkMetrics holds Prometheus metrics for benchmarks
type BenchmarkMetrics struct {
	BenchmarkOpsTotal     *prometheus.CounterVec
	BenchmarkDuration     *prometheus.HistogramVec
	BenchmarkLatency      *prometheus.HistogramVec
	BenchmarkErrors       *prometheus.CounterVec
	BenchmarkMemoryUsage  *prometheus.GaugeVec
	BenchmarkCPUUsage     *prometheus.GaugeVec
}

// NewBenchmarkFramework creates a new benchmark framework instance
func NewBenchmarkFramework(config *BenchmarkConfig) *BenchmarkFramework {
	var metrics *BenchmarkMetrics
	if config.MetricsEnabled {
		metrics = &BenchmarkMetrics{
			BenchmarkOpsTotal: promauto.NewCounterVec(
				prometheus.CounterOpts{
					Name: "benchmark_operations_total",
					Help: "Total number of benchmark operations",
				},
				[]string{"service", "test", "status"},
			),
			BenchmarkDuration: promauto.NewHistogramVec(
				prometheus.HistogramOpts{
					Name:    "benchmark_duration_seconds",
					Help:    "Benchmark operation duration in seconds",
					Buckets: []float64{.001, .005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
				},
				[]string{"service", "test"},
			),
			BenchmarkLatency: promauto.NewHistogramVec(
				prometheus.HistogramOpts{
					Name:    "benchmark_latency_seconds",
					Help:    "Benchmark operation latency in seconds",
					Buckets: []float64{.0001, .0005, .001, .005, .01, .025, .05, .1, .25, .5, 1},
				},
				[]string{"service", "test", "operation"},
			),
			BenchmarkErrors: promauto.NewCounterVec(
				prometheus.CounterOpts{
					Name: "benchmark_errors_total",
					Help: "Total number of benchmark errors",
				},
				[]string{"service", "test", "error_type"},
			),
			BenchmarkMemoryUsage: promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "benchmark_memory_usage_bytes",
					Help: "Benchmark memory usage in bytes",
				},
				[]string{"service", "test"},
			),
			BenchmarkCPUUsage: promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "benchmark_cpu_usage_percent",
					Help: "Benchmark CPU usage percentage",
				},
				[]string{"service", "test"},
			),
		}
	}

	return &BenchmarkFramework{
		config:  config,
		metrics: metrics,
		results: make([]BenchmarkResult, 0),
	}
}

// BenchmarkOperation represents a single operation to be benchmarked
type BenchmarkOperation func(ctx context.Context) error

// RunBenchmark executes a benchmark test with the given operation
func (bf *BenchmarkFramework) RunBenchmark(b *testing.B, operation BenchmarkOperation) {
	ctx := context.Background()
	
	// Warmup phase
	if bf.config.WarmupDuration > 0 {
		bf.runWarmup(ctx, operation)
	}

	// Reset timer after warmup
	b.ResetTimer()
	
	// Run the actual benchmark
	start := time.Now()
	var totalOps int64
	var errors int64
	var latencies []time.Duration
	
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			opStart := time.Now()
			err := operation(ctx)
			latency := time.Since(opStart)
			
			totalOps++
			latencies = append(latencies, latency)
			
			if err != nil {
				errors++
				if bf.metrics != nil {
					bf.metrics.BenchmarkErrors.WithLabelValues(
						bf.config.ServiceName,
						bf.config.TestName,
						"operation_error",
					).Inc()
				}
			}
			
			if bf.metrics != nil {
				bf.metrics.BenchmarkOpsTotal.WithLabelValues(
					bf.config.ServiceName,
					bf.config.TestName,
					"success",
				).Inc()
				bf.metrics.BenchmarkLatency.WithLabelValues(
					bf.config.ServiceName,
					bf.config.TestName,
					"operation",
				).Observe(latency.Seconds())
			}
		}
	})
	
	duration := time.Since(start)
	
	// Calculate statistics
	result := bf.calculateResults(totalOps, duration, latencies, errors)
	bf.recordResult(result)
	
	// Report results
	bf.reportResults(b, result)
}

// runWarmup performs warmup operations
func (bf *BenchmarkFramework) runWarmup(ctx context.Context, operation BenchmarkOperation) {
	warmupEnd := time.Now().Add(bf.config.WarmupDuration)
	for time.Now().Before(warmupEnd) {
		operation(ctx)
	}
}

// calculateResults computes benchmark statistics
func (bf *BenchmarkFramework) calculateResults(totalOps int64, duration time.Duration, latencies []time.Duration, errors int64) BenchmarkResult {
	opsPerSecond := float64(totalOps) / duration.Seconds()
	errorRate := float64(errors) / float64(totalOps) * 100
	
	// Calculate latency percentiles
	avgLatency := bf.calculateAverage(latencies)
	p95Latency := bf.calculatePercentile(latencies, 0.95)
	p99Latency := bf.calculatePercentile(latencies, 0.99)
	
	return BenchmarkResult{
		ServiceName:  bf.config.ServiceName,
		TestName:     bf.config.TestName,
		TotalOps:     totalOps,
		Duration:     duration,
		OpsPerSecond: opsPerSecond,
		AvgLatency:   avgLatency,
		P95Latency:   p95Latency,
		P99Latency:   p99Latency,
		ErrorRate:    errorRate,
		Timestamp:    time.Now(),
	}
}

// calculateAverage computes the average of a slice of durations
func (bf *BenchmarkFramework) calculateAverage(durations []time.Duration) time.Duration {
	if len(durations) == 0 {
		return 0
	}
	
	var total time.Duration
	for _, d := range durations {
		total += d
	}
	return total / time.Duration(len(durations))
}

// calculatePercentile computes the specified percentile of a slice of durations
func (bf *BenchmarkFramework) calculatePercentile(durations []time.Duration, percentile float64) time.Duration {
	if len(durations) == 0 {
		return 0
	}
	
	// Simple percentile calculation (for production, use a proper sorting algorithm)
	index := int(float64(len(durations)) * percentile)
	if index >= len(durations) {
		index = len(durations) - 1
	}
	return durations[index]
}

// recordResult stores the benchmark result
func (bf *BenchmarkFramework) recordResult(result BenchmarkResult) {
	bf.mu.Lock()
	defer bf.mu.Unlock()
	bf.results = append(bf.results, result)
}

// reportResults outputs benchmark results
func (bf *BenchmarkFramework) reportResults(b *testing.B, result BenchmarkResult) {
	b.Logf("Benchmark Results for %s/%s:", result.ServiceName, result.TestName)
	b.Logf("  Total Operations: %d", result.TotalOps)
	b.Logf("  Duration: %v", result.Duration)
	b.Logf("  Operations/sec: %.2f", result.OpsPerSecond)
	b.Logf("  Average Latency: %v", result.AvgLatency)
	b.Logf("  95th Percentile: %v", result.P95Latency)
	b.Logf("  99th Percentile: %v", result.P99Latency)
	b.Logf("  Error Rate: %.2f%%", result.ErrorRate)
}

// GetResults returns all recorded benchmark results
func (bf *BenchmarkFramework) GetResults() []BenchmarkResult {
	bf.mu.RLock()
	defer bf.mu.RUnlock()
	return append([]BenchmarkResult(nil), bf.results...)
}

// GenerateTestData provides utilities for generating test data
type TestDataGenerator struct {
	rand *rand.Rand
}

// NewTestDataGenerator creates a new test data generator
func NewTestDataGenerator() *TestDataGenerator {
	return &TestDataGenerator{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GenerateUserData creates realistic user test data
func (tdg *TestDataGenerator) GenerateUserData() map[string]interface{} {
	return map[string]interface{}{
		"username": fmt.Sprintf("user_%d", tdg.rand.Intn(10000)),
		"email":    fmt.Sprintf("<EMAIL>", tdg.rand.Intn(10000)),
		"category": []string{"PREMIUM", "STANDARD", "BASIC"}[tdg.rand.Intn(3)],
	}
}

// GenerateVoucherData creates realistic voucher test data
func (tdg *TestDataGenerator) GenerateVoucherData() map[string]interface{} {
	return map[string]interface{}{
		"code":        fmt.Sprintf("VOUCHER_%d", tdg.rand.Intn(100000)),
		"discount":    tdg.rand.Float64() * 50, // 0-50% discount
		"category_id": tdg.rand.Intn(10) + 1,
		"max_uses":    tdg.rand.Intn(100) + 1,
	}
}

// GenerateProductData creates realistic product test data
func (tdg *TestDataGenerator) GenerateProductData() map[string]interface{} {
	return map[string]interface{}{
		"name":        fmt.Sprintf("Product_%d", tdg.rand.Intn(1000)),
		"price":       tdg.rand.Float64() * 1000, // $0-1000
		"category_id": tdg.rand.Intn(10) + 1,
		"stock":       tdg.rand.Intn(100),
	}
}
