package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

var (
	grpcSuite     *benchmark.GRPCBenchmarkSuite
	dbSuite       *benchmark.DatabaseBenchmarkSuite
	cacheSuite    *benchmark.CacheBenchmarkSuite
	userClient    user.UserServiceClient
	testDB        *gorm.DB
	testRedis     *redis.Client
	testGenerator *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	// Setup test environment
	if err := setupBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup benchmark environment: %v\n", err)
		os.Exit(1)
	}
	
	// Run benchmarks
	code := m.Run()
	
	// Cleanup
	cleanup()
	
	os.Exit(code)
}

func setupBenchmarkEnvironment() error {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Setup database connection
	logger := logging.New("info", "json")
	appMetrics := metrics.New("user-service-benchmark")
	
	testDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Setup Redis connection
	testRedis, err = redis.NewClient(&cfg.Redis, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	// Setup gRPC connection
	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50053", // User service gRPC port
		ServiceName:    "user-service",
		Timeout:        5 * time.Second,
	}
	
	grpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}
	
	userClient = user.NewUserServiceClient(grpcSuite.GetConnection())

	// Setup database benchmark suite
	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "user-service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	dbSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, testDB)

	// Setup cache benchmark suite
	cacheSuite = benchmark.NewCacheBenchmarkSuite("user-service", testRedis)

	// Setup test data generator
	testGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanup() {
	if grpcSuite != nil {
		grpcSuite.Close()
	}
	if testDB != nil {
		sqlDB, _ := testDB.DB()
		sqlDB.Close()
	}
	if testRedis != nil {
		testRedis.Close()
	}
}

// gRPC Endpoint Benchmarks
func BenchmarkUserService_gRPC_CreateUser(b *testing.B) {
	grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := user.NewUserServiceClient(conn)
		
		// Add authentication metadata
		ctx = addAuthMetadata(ctx)
		
		userData := testGenerator.GenerateUserData()
		req := &user.CreateUserRequest{
			Username: userData["username"].(string),
			Email:    userData["email"].(string),
			Category: user.UserCategory(user.UserCategory_value[userData["category"].(string)]),
		}
		
		_, err := client.CreateUser(ctx, req)
		return err
	})
}

func BenchmarkUserService_gRPC_GetUser(b *testing.B) {
	grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := user.NewUserServiceClient(conn)
		
		// Add authentication metadata
		ctx = addAuthMetadata(ctx)
		
		req := &user.GetUserRequest{
			UserId: int32(testGenerator.GenerateUserID()),
		}
		
		_, err := client.GetUser(ctx, req)
		return err
	})
}

func BenchmarkUserService_gRPC_UpdateUser(b *testing.B) {
	grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := user.NewUserServiceClient(conn)
		
		// Add authentication metadata
		ctx = addAuthMetadata(ctx)
		
		userData := testGenerator.GenerateUserData()
		req := &user.UpdateUserRequest{
			UserId:   int32(testGenerator.GenerateUserID()),
			Username: userData["username"].(string),
			Email:    userData["email"].(string),
			Category: user.UserCategory(user.UserCategory_value[userData["category"].(string)]),
		}
		
		_, err := client.UpdateUser(ctx, req)
		return err
	})
}

func BenchmarkUserService_gRPC_ListUsers(b *testing.B) {
	grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := user.NewUserServiceClient(conn)
		
		// Add authentication metadata
		ctx = addAuthMetadata(ctx)
		
		req := &user.ListUsersRequest{
			Page:     1,
			PageSize: 20,
		}
		
		_, err := client.ListUsers(ctx, req)
		return err
	})
}

// Database Operation Benchmarks
func BenchmarkUserService_Database_Queries(b *testing.B) {
	queryBenchmarks := benchmark.NewDatabaseQueryBenchmarks(dbSuite)
	queryBenchmarks.BenchmarkUserQueries(b)
}

func BenchmarkUserService_Database_CreateUser(b *testing.B) {
	dbSuite.BenchmarkQuery(b, "create_user", func(ctx context.Context, db *gorm.DB) error {
		userData := testGenerator.GenerateUserData()
		user := map[string]interface{}{
			"username":   userData["username"],
			"email":      userData["email"],
			"category":   userData["category"],
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
		return db.Table("users").Create(user).Error
	})
}

func BenchmarkUserService_Database_BulkInsertUsers(b *testing.B) {
	dbSuite.BenchmarkBulkInsert(b, "users", 100, func() interface{} {
		userData := testGenerator.GenerateUserData()
		return map[string]interface{}{
			"username":   userData["username"],
			"email":      userData["email"],
			"category":   userData["category"],
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
	})
}

func BenchmarkUserService_Database_Transaction(b *testing.B) {
	dbSuite.BenchmarkTransaction(b, "user_with_profile", func(ctx context.Context, tx *gorm.DB) error {
		// Create user
		userData := testGenerator.GenerateUserData()
		user := map[string]interface{}{
			"username":   userData["username"],
			"email":      userData["email"],
			"category":   userData["category"],
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
		
		if err := tx.Table("users").Create(user).Error; err != nil {
			return err
		}
		
		// Create user profile (simulated)
		profile := map[string]interface{}{
			"user_id":    user["id"],
			"first_name": "Test",
			"last_name":  "User",
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
		
		return tx.Table("user_profiles").Create(profile).Error
	})
}

// Redis Cache Benchmarks
func BenchmarkUserService_Cache_GetUser(b *testing.B) {
	cacheSuite.BenchmarkCacheGet(b, func(ctx context.Context, key string) error {
		return testRedis.Get(ctx, key, nil)
	})
}

func BenchmarkUserService_Cache_SetUser(b *testing.B) {
	cacheSuite.BenchmarkCacheSet(b, func(ctx context.Context, key string, value interface{}) error {
		return testRedis.Set(ctx, key, value, time.Hour)
	})
}

// Concurrent Load Tests
func BenchmarkUserService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "user-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 10,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "create_user",
				Weight: 20,
				Execute: func(ctx context.Context) error {
					ctx = addAuthMetadata(ctx)
					userData := testGenerator.GenerateUserData()
					req := &user.CreateUserRequest{
						Username: userData["username"].(string),
						Email:    userData["email"].(string),
						Category: user.UserCategory(user.UserCategory_value[userData["category"].(string)]),
					}
					_, err := userClient.CreateUser(ctx, req)
					return err
				},
			},
			{
				Name:   "get_user",
				Weight: 60,
				Execute: func(ctx context.Context) error {
					ctx = addAuthMetadata(ctx)
					req := &user.GetUserRequest{
						UserId: int32(testGenerator.GenerateUserID()),
					}
					_, err := userClient.GetUser(ctx, req)
					return err
				},
			},
			{
				Name:   "list_users",
				Weight: 20,
				Execute: func(ctx context.Context) error {
					ctx = addAuthMetadata(ctx)
					req := &user.ListUsersRequest{
						Page:     1,
						PageSize: 20,
					}
					_, err := userClient.ListUsers(ctx, req)
					return err
				},
			},
		},
	}
	
	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

// Helper functions
func addAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "user-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}

// Add this method to TestDataGenerator
func (tdg *benchmark.TestDataGenerator) GenerateUserID() int {
	return tdg.rand.Intn(1000) + 1
}
